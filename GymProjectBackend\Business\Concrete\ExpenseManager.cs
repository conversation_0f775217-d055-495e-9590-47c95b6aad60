using Business.Abstract;
using Business.Constants;
using Business.ValidationRules.FluentValidation;

using Core.Aspects.Autofac.Validation;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Core.Utilities.Paging;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using Business.BusinessAscpects.Autofac;
using Core.Utilities.Security.CompanyContext;

namespace Business.Concrete
{
    public class ExpenseManager : IExpenseService
    {
        private readonly IExpenseDal _expenseDal;
        private readonly ICompanyContext _companyContext;

        public ExpenseManager(IExpenseDal expenseDal, ICompanyContext companyContext)
        {
            _expenseDal = expenseDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [ValidationAspect(typeof(ExpenseValidator))] // Validator eklendi
        [TransactionScopeAspect]
        public IResult Add(Expense expense)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık business logic'i DAL katmanına taşıdık
            return _expenseDal.AddExpenseWithBusinessLogic(expense, companyId);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        public IResult Delete(int expenseId)
        {
            // SOLID prensiplerine uygun: Complex business logic DAL'a devredildi
            var companyId = _companyContext.GetCompanyId();
            return _expenseDal.SoftDeleteExpense(expenseId, companyId);
        }

        [SecuredOperation("owner,admin")]
        public IDataResult<List<Expense>> GetAll()
        {
            var companyId = _companyContext.GetCompanyId();
            return new SuccessDataResult<List<Expense>>(_expenseDal.GetAll(e => e.CompanyID == companyId && e.IsActive == true  ));
        }

        [SecuredOperation("owner,admin")]
        public IDataResult<Expense> GetById(int expenseId)
        {
            var companyId = _companyContext.GetCompanyId();
            var expense = _expenseDal.Get(e => e.ExpenseID == expenseId && e.CompanyID == companyId && e.IsActive == true);
            if (expense == null)
            {
                return new ErrorDataResult<Expense>(Messages.ExpenseNotFound);
            }
            return new SuccessDataResult<Expense>(expense);
        }

        [SecuredOperation("owner,admin")]
        public IDataResult<List<ExpenseDto>> GetExpensesByDateRange(DateTime startDate, DateTime endDate)
        {
            var result = _expenseDal.GetExpensesByDateRange(startDate, endDate);
            return new SuccessDataResult<List<ExpenseDto>>(result);
        }




        [SecuredOperation("owner,admin")]
        [LogAspect]
        [ValidationAspect(typeof(ExpenseValidator))] // Validator eklendi
        [TransactionScopeAspect]
        public IResult Update(Expense expense)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık business logic'i DAL katmanına taşıdık
            return _expenseDal.UpdateExpenseWithBusinessLogic(expense, companyId);
        }

        [SecuredOperation("owner,admin")]
        public IDataResult<ExpenseDashboardDto> GetExpenseDashboardData(int year, int month)
        {
            var result = _expenseDal.GetExpenseDashboardData(year, month);
            return new SuccessDataResult<ExpenseDashboardDto>(result, "Dashboard verileri başarıyla getirildi.");
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<PaginatedResult<ExpenseDto>> GetExpensesPaginated(ExpensePagingParameters parameters)
        {
            var result = _expenseDal.GetExpensesPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<ExpenseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MonthlyExpenseDto> GetMonthlyExpense(int year)
        {
            try
            {
                var result = _expenseDal.GetMonthlyExpense(year);
                return new SuccessDataResult<MonthlyExpenseDto>(result, $"{year} yılı aylık gider trendi başarıyla getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<MonthlyExpenseDto>(null, "Aylık gider trendi hesaplanırken bir hata oluştu");
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<ExpenseTotals> GetExpenseTotals(ExpensePagingParameters parameters)
        {
            try
            {
                var result = _expenseDal.GetExpenseTotals(parameters);
                return new SuccessDataResult<ExpenseTotals>(result,
                    parameters.StartDate.HasValue || parameters.EndDate.HasValue
                        ? "Filtrelenmiş gider toplamları getirildi"
                        : "Mevcut ay gider toplamları getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<ExpenseTotals>(null, "Gider toplamları hesaplanırken bir hata oluştu");
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<ExpenseDto>> GetAllExpensesFiltered(ExpensePagingParameters parameters)
        {
            var result = _expenseDal.GetAllExpensesFiltered(parameters);
            return new SuccessDataResult<List<ExpenseDto>>(result);
        }
    }
}