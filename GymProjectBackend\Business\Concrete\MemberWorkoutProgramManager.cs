using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;

using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Utilities.Business;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Business.Concrete
{
    public class MemberWorkoutProgramManager : IMemberWorkoutProgramService
    {
        private readonly IMemberWorkoutProgramDal _memberWorkoutProgramDal;
        private readonly ICompanyContext _companyContext;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public MemberWorkoutProgramManager(
            IMemberWorkoutProgramDal memberWorkoutProgramDal,
            ICompanyContext companyContext,
            IHttpContextAccessor httpContextAccessor)
        {
            _memberWorkoutProgramDal = memberWorkoutProgramDal;
            _companyContext = companyContext;
            _httpContextAccessor = httpContextAccessor;
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult AssignProgram(MemberWorkoutProgramAddDto assignmentDto)
        {
            // SOLID prensiplerine uygun: Complex business logic'i DAL katmanına taşıdık
            var companyId = _companyContext.GetCompanyId();
            return _memberWorkoutProgramDal.AssignProgramWithValidation(assignmentDto, companyId);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult UpdateAssignment(MemberWorkoutProgramUpdateDto assignmentDto)
        {
            // SOLID prensiplerine uygun: Business logic DAL katmanına taşındı
            var companyId = _companyContext.GetCompanyId();
            return _memberWorkoutProgramDal.UpdateAssignmentWithValidation(assignmentDto, companyId);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        public IResult DeleteAssignment(int assignmentId)
        {
            // SOLID prensiplerine uygun: Business logic DAL katmanına taşındı
            var companyId = _companyContext.GetCompanyId();
            return _memberWorkoutProgramDal.DeleteAssignmentWithValidation(assignmentId, companyId);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberWorkoutProgramListDto>> GetCompanyAssignments()
        {
            // DAL artık otomatik CompanyID filtreleme yapıyor, manuel parametre gerekmez
            var result = _memberWorkoutProgramDal.GetCompanyAssignments();
            return new SuccessDataResult<List<MemberWorkoutProgramListDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberWorkoutProgramDto>> GetMemberActivePrograms(int memberId)
        {
            // Üyenin şirkete ait olup olmadığını kontrol et
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<MemberWorkoutProgramDto>>(ruleResult.Message);
            }

            var result = _memberWorkoutProgramDal.GetMemberActivePrograms(memberId);
            return new SuccessDataResult<List<MemberWorkoutProgramDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberWorkoutProgramHistoryDto>> GetMemberProgramHistory(int memberId)
        {
            // Üyenin şirkete ait olup olmadığını kontrol et
            IResult ruleResult = BusinessRules.Run(
                CheckIfMemberBelongsToCompany(memberId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<List<MemberWorkoutProgramHistoryDto>>(ruleResult.Message);
            }

            var result = _memberWorkoutProgramDal.GetMemberProgramHistory(memberId);
            return new SuccessDataResult<List<MemberWorkoutProgramHistoryDto>>(result);
        }

        // Mobil API için - User tablosu üzerinden erişim
        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        public IDataResult<List<MemberActiveWorkoutProgramDto>> GetActiveWorkoutProgramsByUserId(int userId)
        {
            // Kullanıcının kendi verilerine eriştiğini kontrol et
            var currentUserIdClaim = _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (currentUserIdClaim == null || !int.TryParse(currentUserIdClaim.Value, out int currentUserId) || currentUserId != userId)
            {
                return new ErrorDataResult<List<MemberActiveWorkoutProgramDto>>("Yetkisiz erişim.");
            }

            var result = _memberWorkoutProgramDal.GetActiveWorkoutProgramsByUserId(userId);
            return new SuccessDataResult<List<MemberActiveWorkoutProgramDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<MemberWorkoutProgramDto> GetAssignmentDetail(int assignmentId)
        {
            var result = _memberWorkoutProgramDal.GetAssignmentDetail(assignmentId);
            if (result == null)
            {
                return new ErrorDataResult<MemberWorkoutProgramDto>("Program ataması bulunamadı.");
            }

            return new SuccessDataResult<MemberWorkoutProgramDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<int> GetAssignedMemberCount(int workoutProgramTemplateId)
        {
            // Programın şirkete ait olup olmadığını kontrol et
            IResult ruleResult = BusinessRules.Run(
                CheckIfProgramBelongsToCompany(workoutProgramTemplateId)
            );

            if (ruleResult != null)
            {
                return new ErrorDataResult<int>(ruleResult.Message);
            }

            var result = _memberWorkoutProgramDal.GetAssignedMemberCount(workoutProgramTemplateId);
            return new SuccessDataResult<int>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        public IDataResult<int> GetActiveAssignmentCount()
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _memberWorkoutProgramDal.GetActiveAssignmentCount(companyId);
            return new SuccessDataResult<int>(result);
        }

        [SecuredOperation("member")]
        [PerformanceAspect(3)]
        public IDataResult<MemberWorkoutProgramDetailDto> GetProgramDetailByUser(int userId, int memberWorkoutProgramId)
        {
            // Kullanıcının kendi verilerine eriştiğini kontrol et
            var currentUserIdClaim = _httpContextAccessor.HttpContext?.User?.Claims?.FirstOrDefault(c => c.Type == "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier");
            if (currentUserIdClaim == null || !int.TryParse(currentUserIdClaim.Value, out int currentUserId) || currentUserId != userId)
            {
                return new ErrorDataResult<MemberWorkoutProgramDetailDto>("Yetkisiz erişim.");
            }

            var result = _memberWorkoutProgramDal.GetProgramDetailByUser(userId, memberWorkoutProgramId);
            if (result == null)
            {
                return new ErrorDataResult<MemberWorkoutProgramDetailDto>(Messages.WorkoutProgramNotFound);
            }
            return new SuccessDataResult<MemberWorkoutProgramDetailDto>(result, Messages.WorkoutProgramDetailRetrieved);
        }

        // SOLID prensiplerine uygun: Business rule'lar DAL katmanına taşındı
        // Artık tüm validation logic'i DAL'da yapılıyor

        // Bu metotlar sadece Get operasyonlarında kullanıldığı için kaldırılmadı
        private IResult CheckIfMemberBelongsToCompany(int memberId)
        {
            var companyId = _companyContext.GetCompanyId();
            var member = _memberWorkoutProgramDal.Get(m => m.MemberID == memberId && m.CompanyID == companyId);
            if (member == null)
            {
                return new ErrorResult("Üye bu şirkete ait değil.");
            }
            return new SuccessResult();
        }

        private IResult CheckIfProgramBelongsToCompany(int workoutProgramTemplateId)
        {
            var companyId = _companyContext.GetCompanyId();
            // Bu kontrol için basit bir sorgu yapıyoruz
            var assignment = _memberWorkoutProgramDal.Get(p => p.WorkoutProgramTemplateID == workoutProgramTemplateId && p.CompanyID == companyId);
            if (assignment == null)
            {
                return new ErrorResult("Antrenman programı bu şirkete ait değil.");
            }
            return new SuccessResult();
        }
    }
}
